<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医疗科普页面演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }
        
        .medical-popular-science {
            position: relative;
            width: 100%;
            height: 100vh;
            background: #fff;
            display: flex;
            flex-direction: column;
        }
        
        .tabs-container {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
            background: #fff;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .tabs-wrapper {
            padding: 20px 16px 10px;
        }
        
        .main-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 16px;
        }
        
        .tabs {
            display: flex;
            justify-content: space-between;
            position: relative;
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            font-size: 14px;
            color: #666;
            padding: 8px 4px;
            cursor: pointer;
            transition: color 0.3s ease;
            position: relative;
        }
        
        .tab-item.active {
            color: #ff1f66;
            font-weight: 500;
        }
        
        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #ff1f66;
            border-radius: 1.5px;
        }
        
        .content-container {
            flex: 1;
            overflow-y: auto;
            padding-top: 120px;
            padding-bottom: 80px;
            -webkit-overflow-scrolling: touch;
        }
        
        .content-section {
            min-height: 100vh;
            padding: 20px 16px;
        }
        
        .content-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333;
            margin-bottom: 20px;
            text-align: justify;
        }
        
        .video-container {
            margin-bottom: 20px;
        }
        
        .video-wrapper {
            position: relative;
            width: 100%;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
        }
        
        .video-cover {
            width: 100%;
            height: 200px;
            object-fit: cover;
            display: block;
            background: #f0f0f0;
        }
        
        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 60px;
            height: 60px;
            background: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .play-button:active {
            transform: translate(-50%, -50%) scale(0.95);
        }
        
        .play-icon {
            width: 0;
            height: 0;
            border-left: 15px solid #fff;
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
            margin-left: 4px;
        }
        
        .video-duration {
            position: absolute;
            bottom: 8px;
            right: 8px;
            background: rgba(0, 0, 0, 0.7);
            color: #fff;
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
        }
        
        .question-prompt {
            font-size: 16px;
            color: #666;
            margin-top: 30px;
        }
        
        .bottom-card {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 99;
            background: #fff;
            border-top: 1px solid #f0f0f0;
            padding: 12px 16px;
            box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #fff;
            border: 1px solid #ff1f66;
            border-radius: 8px;
        }
        
        .card-left {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .doctor-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            object-fit: cover;
            background: #f0f0f0;
        }
        
        .card-info {
            flex: 1;
        }
        
        .card-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .card-subtitle {
            font-size: 12px;
            color: #ff1f66;
        }
        
        .consult-button {
            background: #ff1f66;
            color: #fff;
            border: none;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .consult-button:active {
            transform: scale(0.95);
            background: #e01a5a;
        }
    </style>
</head>
<body>
    <div class="medical-popular-science">
        <!-- 固定的tab栏 -->
        <div class="tabs-container">
            <div class="tabs-wrapper">
                <div class="main-title">讲解回放</div>
                <div class="tabs">
                    <div class="tab-item active" onclick="switchTab(0)">白癜风症状全解析</div>
                    <div class="tab-item" onclick="switchTab(1)">白癜风8大诱因揭秘</div>
                    <div class="tab-item" onclick="switchTab(2)">白癜风治疗方案</div>
                </div>
            </div>
        </div>

        <!-- 滚动内容区域 -->
        <div class="content-container" id="contentContainer">
            <div class="content-section" id="section0">
                <div class="section-content">
                    <p class="content-text">皮肤出现不明原因的白斑？可能是白癜风在作祟！白癜风初期表现为米粒至指甲大小的白色斑片，逐渐发展为瓷白色斑块，常出现在手指、口角等暴露部位。部分患者还会伴随局部瘙痒、毛发变白等症状。</p>
                    
                    <div class="video-container">
                        <div class="video-wrapper" onclick="playVideo('video1')">
                            <div class="video-cover"></div>
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                            <div class="video-duration">03:48</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-section" id="section1">
                <div class="section-content">
                    <p class="content-text">了解白癜风的诱发因素，有助于预防和治疗。主要包括：遗传因素、免疫系统异常、精神压力、外伤刺激、化学物质接触、内分泌失调、微量元素缺乏、感染等8大因素。这些因素可能单独作用，也可能相互影响，共同导致白癜风的发生。</p>
                    
                    <div class="video-container">
                        <div class="video-wrapper" onclick="playVideo('video2')">
                            <div class="video-cover"></div>
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                            <div class="video-duration">04:12</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-section" id="section2">
                <div class="section-content">
                    <p class="content-text">白癜风治疗需要个性化方案，常见治疗方法包括：药物治疗（外用激素、免疫调节剂）、光疗（308激光、UVB）、手术治疗（自体表皮移植）等。早期治疗效果更佳，建议及时就医。治疗过程中需要保持耐心，配合医生制定的治疗计划。</p>
                    
                    <div class="video-container">
                        <div class="video-wrapper" onclick="playVideo('video3')">
                            <div class="video-cover"></div>
                            <div class="play-button">
                                <div class="play-icon"></div>
                            </div>
                            <div class="video-duration">05:20</div>
                        </div>
                    </div>

                    <div class="question-prompt">
                        猜你想问：
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部吸底卡片 -->
        <div class="bottom-card" id="bottomCard">
            <div class="card-content">
                <div class="card-left">
                    <div class="doctor-icon"></div>
                    <div class="card-info">
                        <div class="card-title">领取白癜风修复白斑中药方</div>
                        <div class="card-subtitle">1000人已预约</div>
                    </div>
                </div>
                <button class="consult-button" onclick="handleConsult()">
                    立即咨询
                </button>
            </div>
        </div>
    </div>

    <script>
        let activeTab = 0;
        const contentContainer = document.getElementById('contentContainer');
        const bottomCard = document.getElementById('bottomCard');

        // 切换tab
        function switchTab(index) {
            activeTab = index;
            
            // 更新tab样式
            document.querySelectorAll('.tab-item').forEach((tab, i) => {
                tab.classList.toggle('active', i === index);
            });

            // 滚动到对应section
            const section = document.getElementById(`section${index}`);
            if (section) {
                const offsetTop = section.offsetTop;
                contentContainer.scrollTo({
                    top: offsetTop - 120 - 20,
                    behavior: 'smooth'
                });
            }
        }

        // 处理滚动事件
        contentContainer.addEventListener('scroll', function() {
            const scrollTop = contentContainer.scrollTop;
            const containerHeight = contentContainer.clientHeight;
            const scrollHeight = contentContainer.scrollHeight;

            // 检查是否接近底部（距离底部100px以内）
            const isNearBottom = scrollTop + containerHeight >= scrollHeight - 100;
            
            // 如果是q2c环境，距离底部100px时隐藏底部卡片
            const isQ2C = window.location.search.includes('q2c') || window.location.pathname.includes('q2c');
            if (isQ2C && isNearBottom) {
                bottomCard.style.display = 'none';
            } else {
                bottomCard.style.display = 'block';
            }

            // 计算当前应该激活的tab
            let newActiveTab = 0;
            for (let i = 2; i >= 0; i--) {
                const section = document.getElementById(`section${i}`);
                if (section && scrollTop + 120 + 50 >= section.offsetTop) {
                    newActiveTab = i;
                    break;
                }
            }

            if (newActiveTab !== activeTab) {
                activeTab = newActiveTab;
                document.querySelectorAll('.tab-item').forEach((tab, i) => {
                    tab.classList.toggle('active', i === activeTab);
                });
            }
        });

        // 播放视频
        function playVideo(videoId) {
            console.log('播放视频:', videoId);
            alert('播放视频: ' + videoId);
        }

        // 咨询按钮点击
        function handleConsult() {
            console.log('点击咨询按钮');
            alert('跳转到咨询页面');
        }
    </script>
</body>
</html>
