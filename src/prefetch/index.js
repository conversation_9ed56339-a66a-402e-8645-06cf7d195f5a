/* eslint-disable max-len */
// 不适合用fc的请求预发架构：https://console.cloud.baidu-int.com/devops/icode/repos/baidu/livefe/media-clue/reviews/116148650/files/base...latest/src/templates/live.ejs
/**

- 为什么要压缩：1. sign生成逻辑尽可能避免暴露;2. 打包体积小
- 为什么是 tsup:相对webpack有更干净的产物+更快的构建速度+更少的配置
 */

import {fetchAPIRequst} from './fetch';
import {jsonpAPIRequst} from './jsonp';
import {getTestValue} from './utils';

const globalJumpTestValue = getTestValue();// 只在初始化执行一次
window.globalJumpTestValue = globalJumpTestValue;

function main() {
    try {
        // window.isPerfOptimizeTest = true;
        // jsonpAPIRequst();
        // fetchAPIRequst();
    }
    catch (error) {
        window.prefetchError = error;
    }
}

main();
