@import (reference) '../../styles/common.less';

.aihelper-wrapper {
    max-width: 23.4375rem;
    margin: 0 auto;
    background: #fff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    padding-bottom: 6.25rem;
}

.aihelper-header {
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 100;
    border-bottom: 1px solid #f0f0f0;
}

.aihelper-title {
    text-align: center;
    padding: 1rem 0;
    font-size: 1.125rem;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
}

.aihelper-tabs {
    display: flex;
    justify-content: space-between;
    padding: 0 .5rem;
}

.aihelper-tab {
    font-size: .875rem;
    padding: .5rem .75rem;
    color: #666;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all .3s ease;
    text-align: center;
    flex: 1;
}

.aihelper-tab.active {
    color: #333;
    border-bottom-color: #ff4757;
    font-weight: 500;
}

.aihelper-section {
    padding: 1.25rem;
    border-bottom: 1px solid #f0f0f0;
}

.aihelper-section:last-child {
    border-bottom: none;
}

.aihelper-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
    text-align: center;
}

.aihelper-section-content {
    font-size: .9375rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 1.25rem;
}

.aihelper-video-box {
    position: relative;
    border-radius: .75rem;
    overflow: hidden;
    margin-bottom: 1.25rem;
}

.aihelper-section:last-child .aihelper-video-box {
    margin-bottom: 0;
}

.aihelper-video-img {
    width: 100%;
    height: auto;
    display: block;
}

.aihelper-video-play {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 3.75rem;
    height: 3.75rem;
    background: rgba(0, 0, 0, .6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all .3s ease;
}

.aihelper-video-play:hover {
    background: rgba(0, 0, 0, .8);
    transform: translate(-50%, -50%) scale(1.1);
}

.aihelper-video-play-icon {
    width: 0;
    height: 0;
    border-left: .9375rem solid #fff;
    border-top: .5625rem solid transparent;
    border-bottom: .5625rem solid transparent;
    margin-left: .1875rem;
}

.aihelper-video-time {
    position: absolute;
    bottom: .625rem;
    right: .625rem;
    background: rgba(0, 0, 0, .7);
    color: #fff;
    padding: .25rem .5rem;
    border-radius: .25rem;
    font-size: .75rem;
}

.aihelper-guess {
    padding: 0 1.25rem;
    font-size: .875rem;
    color: #666;
    margin-bottom: 0;
}

.aihelper-banner {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    max-width: 23.4375rem;
    margin: 0;
    border: 2px solid #ff4757;
    border-radius: .75rem .75rem 0 0;
    padding: .9375rem;
    display: flex;
    align-items: center;
    gap: .75rem;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, .1);
    z-index: 99;
}

.aihelper-banner-img-box {
    width: 3.75rem;
    height: 3.75rem;
    border-radius: .5rem;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

.aihelper-banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.aihelper-banner-img-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: .625rem;
    color: #fff;
    text-align: center;
    line-height: 1.2;
}

.aihelper-banner-content {
    flex: 1;
}

.aihelper-banner-title {
    font-size: .9375rem;
    font-weight: 500;
    color: #333;
    margin-bottom: .25rem;
}

.aihelper-banner-desc {
    font-size: .875rem;
    color: #ff4757;
}

.aihelper-banner-btn {
    background: #ff4757;
    color: #fff;
    border: none;
    border-radius: 1.5625rem;
    padding: .75rem 1.25rem;
    font-size: .875rem;
    font-weight: 500;
    cursor: pointer;
    white-space: nowrap;
    transition: all .3s ease;
}

.aihelper-banner-btn:hover {
    background: #ff3742;
}
