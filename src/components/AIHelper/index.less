/*
 * @file 医疗科普页面样式
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@import (reference) './style-util.less';

.test-pr {
    // 1rem=100px
    // 1px=0.01rem
    // 140px=1.4rem
    // width: 1.4rem;
    width: 1rem;
    height: 1rem;
    // height: 1.4rem;
    border: 1px solid red;
}

.medical-popular-science {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;

    // 固定tab栏
    .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #fff;
        border-bottom: 1pr solid #f0f0f0;

        .tabs-wrapper {
            padding: 50pr 40pr 30pr;

            .main-title {
                font-size: 56pr;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 40pr;
            }

            .tabs {
                display: flex;
                justify-content: space-between;
                position: relative;

                .tab-item {
                    flex: 1;
                    text-align: center;
                    font-size: 32pr;
                    color: #666;
                    padding: 20pr 12pr;
                    cursor: pointer;
                    transition: color .3s ease;
                    position: relative;
                    line-height: 1.3;

                    &.active {
                        color: #ff1f66;
                        font-weight: 500;

                        &::after {
                            content: '';
                            position: absolute;
                            bottom: -30pr;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 80pr;
                            height: 8pr;
                            background: #ff1f66;
                            border-radius: 4pr;
                        }
                    }
                }
            }
        }
    }

    // 内容滚动区域
    .content-container {
        flex: 1;
        overflow-y: auto;
        padding-top: 240pr; // 为固定tab栏留出空间
        padding-bottom: 180pr; // 为底部卡片留出空间
        -webkit-overflow-scrolling: touch;

        .content-section {
            min-height: 100vh;
            padding: 50pr 40pr;

            .section-content {
                .content-text {
                    font-size: 36pr;
                    line-height: 1.7;
                    color: #333;
                    margin-bottom: 50pr;
                    text-align: justify;
                }

                .video-container {
                    margin-bottom: 50pr;

                    .video-wrapper {
                        position: relative;
                        width: 100%;
                        border-radius: 20pr;
                        overflow: hidden;
                        cursor: pointer;

                        .video-cover {
                            width: 100%;
                            height: 480pr;
                            object-fit: cover;
                            display: block;
                        }

                        .play-button {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 140pr;
                            height: 140pr;
                            background: rgba(0, 0, 0, .6);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all .3s ease;

                            &:active {
                                transform: translate(-50%, -50%) scale(.95);
                            }

                            .play-icon {
                                width: 0;
                                height: 0;
                                border-left: 36pr solid #fff;
                                border-top: 24pr solid transparent;
                                border-bottom: 24pr solid transparent;
                                margin-left: 10pr;
                            }
                        }

                        .video-duration {
                            position: absolute;
                            bottom: 20pr;
                            right: 20pr;
                            background: rgba(0, 0, 0, .7);
                            color: #fff;
                            font-size: 28pr;
                            padding: 12pr 20pr;
                            border-radius: 10pr;
                        }
                    }
                }

                .question-prompt {
                    font-size: 36pr;
                    color: #666;
                    margin-top: 80pr;
                }
            }
        }
    }

    // 底部吸底卡片
    .bottom-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        background: #fff;
        border-top: 1pr solid #f0f0f0;
        padding: 30pr 40pr;
        box-shadow: 0 -4pr 16pr rgba(0, 0, 0, .1);

        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 24pr;
            background: #fff;
            border: 2pr solid #ff1f66;
            border-radius: 20pr;

            .card-left {
                display: flex;
                align-items: center;
                flex: 1;

                .doctor-icon {
                    width: 96pr;
                    height: 96pr;
                    border-radius: 50%;
                    margin-right: 30pr;
                    object-fit: cover;
                }

                .card-info {
                    flex: 1;

                    .card-title {
                        font-size: 32pr;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 10pr;
                        .text-ellipse(1);
                    }

                    .card-subtitle {
                        font-size: 28pr;
                        color: #ff1f66;
                    }
                }
            }

            .consult-button {
                background: #ff1f66;
                color: #fff;
                border: none;
                border-radius: 48pr;
                padding: 24pr 48pr;
                font-size: 32pr;
                font-weight: 500;
                cursor: pointer;
                transition: all .3s ease;
                white-space: nowrap;

                &:active {
                    transform: scale(.95);
                    background: #e01a5a;
                }
            }
        }
    }
}

// 响应式适配 - 小屏手机
@media screen and (max-width: 375px) {
    .medical-popular-science {
        .tabs-container .tabs-wrapper {
            padding: 40pr 30pr 24pr;

            .main-title {
                font-size: 50pr;
                margin-bottom: 32pr;
            }

            .tabs .tab-item {
                font-size: 28pr;
                padding: 16pr 8pr;
                line-height: 1.2;

                &.active::after {
                    width: 60pr;
                    height: 6pr;
                    bottom: -24pr;
                }
            }
        }

        .content-container {
            padding-top: 220pr;
            padding-bottom: 160pr;

            .content-section {
                padding: 40pr 30pr;

                .section-content {
                    .content-text {
                        font-size: 32pr;
                        line-height: 1.6;
                        margin-bottom: 40pr;
                    }

                    .video-container {
                        margin-bottom: 40pr;

                        .video-wrapper {
                            .video-cover {
                                height: 400pr;
                            }

                            .play-button {
                                width: 120pr;
                                height: 120pr;

                                .play-icon {
                                    border-left: 30pr solid #fff;
                                    border-top: 20pr solid transparent;
                                    border-bottom: 20pr solid transparent;
                                    margin-left: 8pr;
                                }
                            }

                            .video-duration {
                                font-size: 24pr;
                                padding: 10pr 16pr;
                                bottom: 16pr;
                                right: 16pr;
                            }
                        }
                    }

                    .question-prompt {
                        font-size: 32pr;
                        margin-top: 60pr;
                    }
                }
            }
        }

        .bottom-card {
            padding: 24pr 30pr;

            .card-content {
                padding: 20pr;

                .card-left {
                    .doctor-icon {
                        width: 80pr;
                        height: 80pr;
                        margin-right: 24pr;
                    }

                    .card-info {
                        .card-title {
                            font-size: 28pr;
                            margin-bottom: 8pr;
                        }

                        .card-subtitle {
                            font-size: 24pr;
                        }
                    }
                }

                .consult-button {
                    padding: 20pr 40pr;
                    font-size: 28pr;
                    border-radius: 40pr;
                }
            }
        }
    }
}

// 超小屏手机适配
@media screen and (max-width: 320px) {
    .medical-popular-science {
        .tabs-container .tabs-wrapper {
            padding: 36pr 24pr 20pr;

            .main-title {
                font-size: 46pr;
                margin-bottom: 28pr;
            }

            .tabs .tab-item {
                font-size: 26pr;
                padding: 14pr 6pr;
            }
        }

        .content-container {
            padding-top: 200pr;

            .content-section {
                padding: 36pr 24pr;

                .section-content {
                    .content-text {
                        font-size: 30pr;
                        margin-bottom: 36pr;
                    }

                    .video-container .video-wrapper {
                        .video-cover {
                            height: 360pr;
                        }

                        .play-button {
                            width: 100pr;
                            height: 100pr;

                            .play-icon {
                                border-left: 24pr solid #fff;
                                border-top: 16pr solid transparent;
                                border-bottom: 16pr solid transparent;
                                margin-left: 6pr;
                            }
                        }
                    }
                }
            }
        }

        .bottom-card {
            padding: 20pr 24pr;

            .card-content {
                padding: 16pr;

                .card-left {
                    .doctor-icon {
                        width: 70pr;
                        height: 70pr;
                        margin-right: 20pr;
                    }

                    .card-info {
                        .card-title {
                            font-size: 26pr;
                        }

                        .card-subtitle {
                            font-size: 22pr;
                        }
                    }
                }

                .consult-button {
                    padding: 18pr 32pr;
                    font-size: 26pr;
                }
            }
        }
    }
}
