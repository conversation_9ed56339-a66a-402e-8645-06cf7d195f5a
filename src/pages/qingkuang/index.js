/* eslint-disable no-unused-vars */
import React, {useEffect} from 'react';
import {hydrate, render} from 'react-dom';
import {registerPageInit} from '@/pages/liveshow/service/register';
import bmls from '@baidu/bdmedialive-scheme';
import once from 'lodash/once';
import AIHelper from '@/components/AIHelper';

import '../liveshow/assets/index.less';
import './index.less';
import 'antd/es/button/style';

// 注册所有初始化逻辑
registerPageInit();
const enterMonitor = once(() => {
    // adCommonShowLog({
    //     // page写死 0: 'live'，中间页无需区分直播状态
    //     page: '0', type: 'enter', value: 'entry', serverId: '19463', isNewVersion: true
    // });
});
function App() {
    enterMonitor();
    useEffect(() => {
        bmls.common.toast({
            message: 'hello qingkuang!'
        });
    }, []);
    function openBdMediaLiveWithParams(params = {}) {
        const encodedParams = encodeURIComponent(JSON.stringify(params));
        const url = `bdmedialive://bdmedialive/common/clueWinnowerEvent?params=${encodedParams}`;
        location.href = url;
    }

    function test1() {
        openBdMediaLiveWithParams({
            type: '1',
            playUrl: 'https://fc-video.cdn.bcebos.com/a870d924cc63dd60102da2725a64243c.mp4',
            coverImgUrl: 'https://fc-video.cdn.bcebos.com/5f1ce09a996141ce9421e3d6e45f4e0a.jpg'
        });
    }
    function test2() {
        openBdMediaLiveWithParams({type: '2'});
    }

    function test3() {
        openBdMediaLiveWithParams({type: '3'});
    }
    function showBmlsToast(message) {
        const params = {message};
        const encodedParams = encodeURIComponent(JSON.stringify(params));
        const url = `bmls://bdmedialive/common/toast?params=${encodedParams}`;
        location.href = url;
    }

    return <div className='qingkuang-app-container'>
        <AIHelper />
    </div>;
}

const containerId = 'qingkuang-root';
const rootElement = window.document.getElementById(containerId);
if (rootElement) {
    if (rootElement.hasChildNodes()) {
        hydrate(<App />, rootElement);
    }
    else {
        render(<App />, rootElement);
    }
}

export default locals => {
    return Promise.resolve(
        locals.preRender({
            renderer: () => '',
            id: containerId,
            main: App,
            props: {}
        })
    );
};
