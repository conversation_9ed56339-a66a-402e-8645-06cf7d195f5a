/* eslint-disable */ // ! 调试方式：babel/swc编译本文件后copy到tmpInit-prod.js下
module.exports = function(doc, win) {
    function _array_like_to_array(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _array_with_holes(arr) {
    if (Array.isArray(arr)) return arr;
}
function _array_without_holes(arr) {
    if (Array.isArray(arr)) return _array_like_to_array(arr);
}
function _iterable_to_array(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _iterable_to_array_limit(arr, i) {
    var _i = arr == null ? null : typeof Symbol !== "undefined" && arr[Symbol.iterator] || arr["@@iterator"];
    if (_i == null) return;
    var _arr = [];
    var _n = true;
    var _d = false;
    var _s, _e;
    try {
        for(_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true){
            _arr.push(_s.value);
            if (i && _arr.length === i) break;
        }
    } catch (err) {
        _d = true;
        _e = err;
    } finally{
        try {
            if (!_n && _i["return"] != null) _i["return"]();
        } finally{
            if (_d) throw _e;
        }
    }
    return _arr;
}
function _non_iterable_rest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _non_iterable_spread() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _sliced_to_array(arr, i) {
    return _array_with_holes(arr) || _iterable_to_array_limit(arr, i) || _unsupported_iterable_to_array(arr, i) || _non_iterable_rest();
}
function _to_consumable_array(arr) {
    return _array_without_holes(arr) || _iterable_to_array(arr) || _unsupported_iterable_to_array(arr) || _non_iterable_spread();
}
function _unsupported_iterable_to_array(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _array_like_to_array(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _array_like_to_array(o, minLen);
}
    var signed_crc_table = function signed_crc_table() {
        var c = 0;
        var table = new Array(256);
        for(var n = 0; n != 256; ++n){
            c = n;
            for(var k = 0; k < 8; ++k){
                c = c & 1 ? -306674912 ^ c >>> 1 : c >>> 1;
            }
            table[n] = c;
        }
        return typeof Int32Array !== 'undefined' ? new Int32Array(table) : table;
    };
    var slice_by_16_tables = function slice_by_16_tables(T) {
        var c = 0;
        var v = 0;
        var n = 0;
        var table = typeof Int32Array !== 'undefined' ? new Int32Array(4096) : new Array(4096);
        for(n = 0; n != 256; ++n){
            table[n] = T[n];
        }
        for(n = 0; n != 256; ++n){
            v = T[n];
            for(c = 256 + n; c < 4096; c += 256){
                v = table[c] = v >>> 8 ^ T[v & 0xFF];
            }
        }
        var out = [];
        for(n = 1; n != 16; ++n){
            out[n - 1] = typeof Int32Array !== 'undefined' ? table.subarray(n * 256, n * 256 + 256) : table.slice(n * 256, n * 256 + 256);
        }
        return out;
    };
    var crc32_bstr = function crc32_bstr(bstr, seed) {
        var C = seed ^ -1;
        for(var i = 0, L = bstr.length; i < L;){
            C = C >>> 8 ^ T0[(C ^ bstr.charCodeAt(i++)) & 0xFF];
        }
        return ~C;
    };
    var crc32_buf = function crc32_buf(B, seed) {
        var C = seed ^ -1;
        var L = B.length - 15;
        var i = 0;
        for(; i < L;){
            C = Tf[B[i++] ^ C & 255] ^ Te[B[i++] ^ C >> 8 & 255] ^ Td[B[i++] ^ C >> 16 & 255] ^ Tc[B[i++] ^ C >>> 24] ^ Tb[B[i++]] ^ Ta[B[i++]] ^ T9[B[i++]] ^ T8[B[i++]] ^ T7[B[i++]] ^ T6[B[i++]] ^ T5[B[i++]] ^ T4[B[i++]] ^ T3[B[i++]] ^ T2[B[i++]] ^ T1[B[i++]] ^ T0[B[i++]];
        }
        L += 15;
        while(i < L){
            C = C >>> 8 ^ T0[(C ^ B[i++]) & 0xFF];
        }
        return ~C;
    };
    var crc32_str = function crc32_str(str, seed) {
        var C = seed ^ -1;
        for(var i = 0, L = str.length, c = 0, d = 0; i < L;){
            c = str.charCodeAt(i++);
            if (c < 0x80) {
                C = C >>> 8 ^ T0[(C ^ c) & 0xFF];
            } else if (c < 0x800) {
                C = C >>> 8 ^ T0[(C ^ (192 | c >> 6 & 31)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | c & 63)) & 0xFF];
            } else if (c >= 0xD800 && c < 0xE000) {
                c = (c & 1023) + 64;
                d = str.charCodeAt(i++) & 1023;
                C = C >>> 8 ^ T0[(C ^ (240 | c >> 8 & 7)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | c >> 2 & 63)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | d >> 6 & 15 | (c & 3) << 4)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | d & 63)) & 0xFF];
            } else {
                C = C >>> 8 ^ T0[(C ^ (224 | c >> 12 & 15)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | c >> 6 & 63)) & 0xFF];
                C = C >>> 8 ^ T0[(C ^ (128 | c & 63)) & 0xFF];
            }
        }
        return ~C;
    };
    var once = function once(fn) {
        var called = false;
        var result;
        return function() {
            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                args[_key] = arguments[_key];
            }
            if (!called) {
                called = true;
                result = fn.apply(void 0, _to_consumable_array(args));
            }
            return result;
        };
    };
    var getHashBucket = /**
 * 将字符串映射到 [0, bucketCount-1] 的桶编号
 */ function getHashBucket(str) {
        var bucketCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 100;
        var hash = crc32.str(str);
        var unsigned = hash >>> 0;
        return unsigned % bucketCount;
    };
    var getTrafficBucket = /**
 * 通用流量分流函数
 * @param options.key 参与 hash 的参数名（从 URL 中获取）
 * @param options.buckets 分流桶定义，如：{ test: 50, origin: 50 }
 * @param options.defaultKey 可选，如果无法分流，返回的默认桶名
 */ function getTrafficBucket(options) {
        var buckets = options.buckets;
        var id = bd_vid;
        var bucketNames = Object.keys(buckets);
        var totalWeight = Object.values(buckets).reduce(function(sum, val) {
            return sum + val;
        }, 0);
        var ratio = -1;
        if (id) {
            var bucketIndex = getHashBucket(id, 10000); // 高精度 hash
            ratio = bucketIndex / 10000;
        } else {
            ratio = Math.random();
        }
        var acc = 0;
        var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;
        try {
            for(var _iterator = bucketNames[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){
                var name = _step.value;
                acc += buckets[name] / totalWeight;
                if (ratio < acc) {
                    return name;
                }
            }
        } catch (err) {
            _didIteratorError = true;
            _iteratorError = err;
        } finally{
            try {
                if (!_iteratorNormalCompletion && _iterator.return != null) {
                    _iterator.return();
                }
            } finally{
                if (_didIteratorError) {
                    throw _iteratorError;
                }
            }
        }
        return bucketNames[bucketNames.length - 1];
    };
    var getUrlParam = function getUrlParam(name) {
        var reg = new RegExp('(^|(&|/?))' + name + '=([^&]*)(&|$)', 'i');
        var r = win.location.search.substr(1).match(reg);
        if (r !== null) {
            return r[3];
        }
        return null;
    };
    var checkHasValue = function checkHasValue(value) {
        if (value) {
            var ignore = [
                'nil',
                'null',
                'nullb',
                'undefined',
                '0'
            ];
            var check = true;
            ignore.forEach(function(item) {
                if (item === value) {
                    check = false;
                }
            });
            return check;
        }
        return false;
    };
    var transUrlParam = function transUrlParam(name) {
        var value = getUrlParam(name);
        return checkHasValue(value) ? '&' + name + '=' + value : '';
    };
    var isMobileOld = function isMobileOld() {
        return /android.*?mobile|ipod|blackberry|bb\d+|phone|WindowsWechat/i.test(win.navigator.userAgent);
    };
    var isMobileNew = function isMobileNew() {
        // eslint-disable-next-line
        return /android|ipod|iPad|blackberry|bb\d+|phone|WindowsWechat|baiduboxapp|bdhonorbrowser/i.test(win.navigator.userAgent);
    };
    var isPADMac = function isPADMac() {
        return /iPad/i.test(win.navigator.userAgent) || win.navigator.platform === 'MacIntel' && win.navigator.maxTouchPoints > 1;
    };
    var isTablet = function isTablet() {
        var ua = win.navigator.userAgent.toLowerCase();
        // 2. UA 检测
        return /ipad|tablet|android(?!.*mobile)/i.test(ua);
    };
    var T0 = signed_crc_table();
    var TT = slice_by_16_tables(T0);
    var _TT = _sliced_to_array(TT, 15), T1 = _TT[0], T2 = _TT[1], T3 = _TT[2], T4 = _TT[3], T5 = _TT[4], T6 = _TT[5], T7 = _TT[6], T8 = _TT[7], T9 = _TT[8], Ta = _TT[9], Tb = _TT[10], Tc = _TT[11], Td = _TT[12], Te = _TT[13], Tf = _TT[14];
    var crc32 = {
        table: T0,
        bstr: crc32_bstr,
        buf: crc32_buf,
        str: crc32_str
    };
    var getUrlAllParam = function() {
        var link = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '';
        var paramStr = '';
        if (link && typeof link === 'string') {
            paramStr = link.substring(link.indexOf('?') + 1, link.length).replace(/#\/$/, '');
        } else {
            var hash = window.location.hash;
            paramStr = window.location.search.substr(1) || hash.substring(hash.indexOf('?') + 1, hash.length);
        }
        var urlList = paramStr.split('&');
        var urlObject = {};
        urlList.forEach(function(item) {
            var urlItem = item.split('=');
            if (urlItem[1]) {
                urlObject[urlItem[0]] = decodeURIComponent(urlItem[1]);
            }
        });
        return urlObject;
    };
    var getCachedUrlParams = once(function() {
        return getUrlAllParam(location.href);
    });
    var isPAD = function isPAD() {
        return isTablet() || isPADMac();
    };
    var APP_NAME_PARAM = {
        a6: '百度看看',
        i6: '百度看看',
        haokan: '好看视频',
        default: '百度',
        lite: '百度极速版',
        tieba: '百度贴吧',
        youjia: '有驾',
        baidudict: '百度汉语',
        fortunecat: '古物潮玩',
        hiphop: '音磁'
    };
    var bd_vid = getUrlParam('bd_vid');
    var inPCTest = getTrafficBucket({
        buckets: {
            test: 50,
            origin: 50
        }
    });
    window.inPCTest = inPCTest;
    var condition = inPCTest ? !isMobileNew() && !isPAD() : !isMobileOld();
    if (condition) {
        var roomid = getUrlParam('roomid') || getUrlParam('room_id');
        var source = getUrlParam('source') || 'h5pre';
        var askID = transUrlParam('ask_id');
        var replaySlice = transUrlParam('replay_slice');
        var nid = transUrlParam('nid');
        if (roomid) {
            var newHost = 'https://live.baidu.com/m/media/pclive/pchome/live.html?room_id=' + roomid + '&source=' + source + askID;
            if (replaySlice && nid) {
                newHost = newHost + replaySlice + nid;
            }
            win.location.href = newHost;
        }
    }
    var urlSource = getUrlParam('source');
    if (APP_NAME_PARAM[urlSource]) {
        doc.title = APP_NAME_PARAM[urlSource] + '直播';
    }
};

