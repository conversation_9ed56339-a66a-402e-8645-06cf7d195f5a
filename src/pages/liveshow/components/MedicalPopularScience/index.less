/*
 * @file 医疗科普页面样式
 * <AUTHOR> Assistant
 * @date 2025-01-25
 */
@import (reference) '../../assets/style-util.less';

.medical-popular-science {
    position: relative;
    width: 100%;
    height: 100vh;
    background: #fff;
    display: flex;
    flex-direction: column;

    // 固定tab栏
    .tabs-container {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        background: #fff;
        border-bottom: 1pr solid #f0f0f0;

        .tabs-wrapper {
            padding: 40pr 32pr 20pr;

            .main-title {
                font-size: 48pr;
                font-weight: 600;
                color: #333;
                text-align: center;
                margin-bottom: 32pr;
            }

            .tabs {
                display: flex;
                justify-content: space-between;
                position: relative;

                .tab-item {
                    flex: 1;
                    text-align: center;
                    font-size: 28pr;
                    color: #666;
                    padding: 16pr 8pr;
                    cursor: pointer;
                    transition: color .3s ease;
                    position: relative;

                    &.active {
                        color: #ff1f66;
                        font-weight: 500;

                        &::after {
                            content: '';
                            position: absolute;
                            bottom: -20pr;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 60pr;
                            height: 6pr;
                            background: #ff1f66;
                            border-radius: 3pr;
                        }
                    }
                }
            }
        }
    }

    // 内容滚动区域
    .content-container {
        flex: 1;
        overflow-y: auto;
        padding-top: 200pr; // 为固定tab栏留出空间
        padding-bottom: 160pr; // 为底部卡片留出空间
        -webkit-overflow-scrolling: touch;

        .content-section {
            min-height: 100vh;
            padding: 40pr 32pr;

            .section-content {
                .content-text {
                    font-size: 32pr;
                    line-height: 1.6;
                    color: #333;
                    margin-bottom: 40pr;
                    text-align: justify;
                }

                .video-container {
                    margin-bottom: 40pr;

                    .video-wrapper {
                        position: relative;
                        width: 100%;
                        border-radius: 16pr;
                        overflow: hidden;
                        cursor: pointer;

                        .video-cover {
                            width: 100%;
                            height: 400pr;
                            object-fit: cover;
                            display: block;
                        }

                        .play-button {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            width: 120pr;
                            height: 120pr;
                            background: rgba(0, 0, 0, .6);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all .3s ease;

                            &:active {
                                transform: translate(-50%, -50%) scale(.95);
                            }

                            .play-icon {
                                width: 0;
                                height: 0;
                                border-left: 30pr solid #fff;
                                border-top: 20pr solid transparent;
                                border-bottom: 20pr solid transparent;
                                margin-left: 8pr;
                            }
                        }

                        .video-duration {
                            position: absolute;
                            bottom: 16pr;
                            right: 16pr;
                            background: rgba(0, 0, 0, .7);
                            color: #fff;
                            font-size: 24pr;
                            padding: 8pr 16pr;
                            border-radius: 8pr;
                        }
                    }
                }

                .question-prompt {
                    font-size: 32pr;
                    color: #666;
                    margin-top: 60pr;
                }
            }
        }
    }

    // 底部吸底卡片
    .bottom-card {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 99;
        background: #fff;
        border-top: 1pr solid #f0f0f0;
        padding: 24pr 32pr;
        box-shadow: 0 -4pr 16pr rgba(0, 0, 0, .1);

        .card-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20pr;
            background: #fff;
            border: 2pr solid #ff1f66;
            border-radius: 16pr;

            .card-left {
                display: flex;
                align-items: center;
                flex: 1;

                .doctor-icon {
                    width: 80pr;
                    height: 80pr;
                    border-radius: 50%;
                    margin-right: 24pr;
                    object-fit: cover;
                }

                .card-info {
                    flex: 1;

                    .card-title {
                        font-size: 28pr;
                        font-weight: 500;
                        color: #333;
                        margin-bottom: 8pr;
                        .text-ellipse(1);
                    }

                    .card-subtitle {
                        font-size: 24pr;
                        color: #ff1f66;
                    }
                }
            }

            .consult-button {
                background: #ff1f66;
                color: #fff;
                border: none;
                border-radius: 40pr;
                padding: 20pr 40pr;
                font-size: 28pr;
                font-weight: 500;
                cursor: pointer;
                transition: all .3s ease;
                white-space: nowrap;

                &:active {
                    transform: scale(.95);
                    background: #e01a5a;
                }
            }
        }
    }
}

// 响应式适配
@media screen and (max-width: 375px) {
    .medical-popular-science {
        .tabs-container .tabs-wrapper {
            padding: 32pr 24pr 16pr;

            .main-title {
                font-size: 44pr;
                margin-bottom: 24pr;
            }

            .tabs .tab-item {
                font-size: 26pr;
                padding: 12pr 4pr;
            }
        }

        .content-container {
            padding-top: 180pr;

            .content-section {
                padding: 32pr 24pr;

                .section-content .content-text {
                    font-size: 30pr;
                }
            }
        }

        .bottom-card {
            padding: 20pr 24pr;

            .card-content {
                padding: 16pr;

                .card-left .doctor-icon {
                    width: 70pr;
                    height: 70pr;
                    margin-right: 20pr;
                }

                .consult-button {
                    padding: 16pr 32pr;
                    font-size: 26pr;
                }
            }
        }
    }
}
