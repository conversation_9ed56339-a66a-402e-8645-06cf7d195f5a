/*
 * @file 商业直播样式
 * <AUTHOR>
 * @date 2025-01-10 17:03:31
 */
@import (reference) '../../assets/style-util.less';

html {
    overflow: hidden;
}

// 当前屏幕为横屏
body.landscape {
    .ad-live-wrapper {
        height: auto;

        .screen-wrapper-container {
            transform: translate(0, 0) !important;

            .video-screen {
                min-height: 2244pr;
                padding-top: 426pr;
            }
        }

        .recommend-screen .live-list-wrapper {
            min-height: unset;
        }

        &.is-end-no-review {
            .video-screen {
                display: none;
            }
        }
    }
}

.ad-live-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: PingFangSC-Regular;

    .s-image-wrapper .s-image-default {
        background-position: center center;
        background-size: 70% 70%;
    }

    .whole-invoke {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0);
        z-index: 5;
    }

    .screen-bg {
        width: 100%;
        height: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        transform: scale(1.2);
        filter: blur(15px);
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        pointer-events: none; // 在手百内长按页面，禁止读取该背景图，和视频长按快进/快退手势冲突

        &::before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background-color: rgba(6, 6, 6, .7);
        }
    }
    // CNY直播间 展示封面，无透明度，无模糊
    .show-cover {
        &::before {
            content: '';
            display: block;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0);
        }

        filter: blur(0);
    }

    .screen-bg-new {
        width: 100%;
        height: 100%;
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        background-size: cover;
        background-position: center center;
        background-repeat: no-repeat;
        pointer-events: none; // 在手百内长按页面，禁止读取该背景图，和视频长按快进/快退手势冲突
    }

    .screen-wrapper-container {
        width: 100%;
        height: 100%;
        position: relative;
        transition-duration: 200ms;
    }

    .page-screen {
        overflow: hidden;
        position: relative;
        width: 100%;
        height: 100%;

        .inner-scroll {
            height: 100%;
            overflow-y: scroll;
            -webkit-overflow-scrolling: touch;
            position: relative;
        }
    }

    // 视频页
    .video-screen {
        &::before,
        &::after {
            pointer-events: none;
            content: '';
            display: block;
            width: 100%;
            position: absolute;
            opacity: .2;
        }

        &::before {
            top: -720pr;
            left: 0;
            height: 1131pr;
            background-image: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, #FFF);
        }

        &::after {
            bottom: 0;
            left: 0;
            height: 1131pr;
            background-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFF);
        }

        .room-head-wrapper,
        .bottom-bar-wrapper,
        .im-wrapper {
            width: 100%;
            position: absolute;
            left: 0;
            z-index: 1;
        }

        .room-head-wrapper {
            top: 0;
        }

        .im-wrapper {
            bottom: 366pr;
            display: flex;
            justify-content: flex-end;
            align-items: flex-end;

            .static-pendant-wrapper {
                position: unset;
                flex: none;
            }
        }

        .im-wrapper-cny {
            bottom: 1.63rem;
        }

        .bottom-bar-wrapper {
            // TODO：看看为啥这样不行
            // padding: 0 30pr;
            bottom: 108pr;
            display: flex;
            justify-content: flex-end;

            .bottom-author-info-wrapper {
                margin-left: 30pr;
            }
        }

        .show-input-bottom-bar-wrapper {
            padding: 0 !important;
            background-color: #fff;
            margin: 0 auto;
            bottom: 0 !important;
        }

        .cny-bottom-bar-wrapper {
            padding: 0;
            bottom: 0;
        }
    }

    // 更多直播推荐页
    .recommend-screen {
        .reflow-btn-wrapper.top-btn {
            position: absolute !important;
            top: 0;
            left: 0;
        }

        .reflow-btn-wrapper-inner.top-btn {
            width: 100%;
            display: flex;
            justify-content: space-between;
        }

        .reflow-btn-wrapper.hover-reflow-btn {
            position: absolute !important;
            bottom: 90pr;
            left: 50%;
            transform: translateX(-50%);
            z-index: 50;
        }

        .inner-scroll {
            display: flex;
            flex-direction: column;
        }

        .live-room-closed-wrapper {
            flex: 1 0 auto;
        }

        .live-list-wrapper {
            flex: 1 0 auto;
        }
    }

    .room-head-wrapper.is-map-attractions {
        position: absolute;
        top: 0;
        left: 0;
    }

    .live-room-closed-wrapper.is-map-attractions {
        height: 100%;
        z-index: 1;
        position: relative;
    }
}

// 没有推荐列表的情况
.ad-live-wrapper.no-recommend-list {
    .live-room-closed-wrapper {
        height: 100%;
    }
}

// 视频直播中
.ad-live-wrapper.is-living-video {
    .im-wrapper {
        bottom: 252pr;
    }

    .im-wrapper-cny {
        bottom: 1.63rem;
    }
}

// 视频横屏
.ad-live-wrapper.is-hor-video {
    .video-screen {
        display: flex;
        flex-direction: column;
        padding-bottom: 258pr;
        padding-top: 15vh;

        .live-video-wrapper {
            z-index: 6;
        }

        .im-wrapper {
            flex: 1;
            overflow: hidden;
            position: relative;
            left: 0;
            bottom: unset;
            margin-top: 180pr;

            .chat-msg-wrapper {
                height: 100%;
                max-height: unset;
            }
        }
    }
}

// 视频横屏-cny
.ad-live-wrapper.is-hor-video.is-cny {
    .video-screen {
        padding-bottom: 489pr;
    }
}

// 直播结束无回放
.ad-live-wrapper.is-end-no-review {
    .error-wrapper {
        display: none;
    }
}

.preview-chat-bar {
    bottom: 10px !important;
}

.door {
    width: 70px;
    height: 100px;
    border-radius: 5px;
    position: fixed;
    background: yellow;
    bottom: 40px;
    right: 20px;
    line-height: 100px;
    text-align: center;
    z-index: 10000;
}

.optimize-door-container {
    background-color: gray;
    overflow: hidden;

    .ad-chat-msg-wrapper {
        overflow: hidden;
        height: 19vh;
    }

    .im-wrapper,
    .optimize-door-wrapper {
        padding-top: 12px;
    }
}

.shot-width {
    width: 72% !important;
}

.im-door-flex-wrapper {
    display: flex;
}

/* iframe弹窗动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        transform: translateY(0);
        opacity: 1;
    }

    to {
        transform: translateY(100%);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }

    to {
        opacity: 0;
    }
}

/* iframe背景蒙层 */
.door-iframe-overlay {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, .35);
    z-index: 9998;
    animation: fadeIn .2s ease-out;

    &.closing {
        animation: fadeOut .25s ease-in;
        animation-fill-mode: forwards;
    }
}

/* iframe容器样式 */
.door-iframe-wrapper {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 75%;
    z-index: 9999;
    background: #fff;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    animation: slideUp .25s cubic-bezier(.25, .46, .45, .94);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, .15);

    &.closing {
        animation: slideDown .25s cubic-bezier(.4, 0, .6, 1);
        animation-fill-mode: forwards;
    }

    &:hover::before {
        background: rgba(0, 0, 0, .2);
    }

    &:hover::after {
        color: #333;
    }
}

/* 圆角区域的额外蒙层 */
.door-iframe-corner-mask {
    position: absolute;
    top: -20px;
    left: 0;
    right: 0;
    height: 20px;
    background: rgba(0, 0, 0, .35);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    z-index: -1;
}

/* iframe样式 */
.door-iframe {
    width: 100%;
    height: 100%;
    border: none;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}
