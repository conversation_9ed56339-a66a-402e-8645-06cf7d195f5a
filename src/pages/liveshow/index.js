import React, {Suspense} from 'react';
import Loading from '@/pages/liveshow/components/Loading';
import {hydrate, render} from 'react-dom';
import {registerPageInit} from '@/pages/liveshow/service/register';
import {env} from '@/utils/env';
import '@/pages/liveshow/utils/errorMonitor';
import './assets/index.less';
import boxjsUtils from './utils/boxjsUtils';
import {WithWeirwoodCaughtBoundaryConfigProvider} from './WithWeirwoodCaughtBoundaryConfigProvider';
import New from './new';

// 注册所有初始化逻辑
registerPageInit();

// hide NA Loading
if (+window?.staticContext?.mainRes?.errno === 0) {
    env.isBox && boxjsUtils.invokeSchema({schema: 'baiduboxapp://v19/hideLoading'});
}

// 异步导入组件

// const enterMonitor = once(() => {
//     adCommonShowLog({
//         page: 'live', type: 'enter', value: 'entry', serverId: '19336', isNewVersion: true
//     });
// });
function App() {
    return (
        <WithWeirwoodCaughtBoundaryConfigProvider>
            <Suspense fallback={<Loading />}>
                <New />
            </Suspense>
        </WithWeirwoodCaughtBoundaryConfigProvider>
    );
}

const containerId = 'root';
const rootElement = window.document.getElementById(containerId);
if (rootElement) {
    if (rootElement.hasChildNodes()) {
        hydrate(<App />, rootElement);
    }
    else {
        render(<App />, rootElement);
    }
}

export default locals => {
    return Promise.resolve(
        locals.preRender({
            renderer: () => '',
            id: containerId,
            main: App,
            props: {}
        })
    );
};
